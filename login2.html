?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Text Detector - Login</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <style>
        :root {
            --color-primary: #6366f1;
            --color-primary-dark: #4f46e5;
            --color-success: #10b981;
            --color-danger: #ef4444;
            --color-warning: #f59e0b;

            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --bg-tertiary: #f1f5f9;

            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --text-tertiary: #94a3b8;

            --border-primary: #e2e8f0;
            --border-secondary: #cbd5e1;

            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

            --radius-md: 0.375rem;
            --radius-lg: 0.5rem;
            --radius-xl: 0.75rem;

            --transition-fast: 150ms ease-in-out;
            --transition-normal: 250ms ease-in-out;
        }

        /* Dark Mode Variables */
        body.dark-mode {
            --bg-primary: #1e293b;
            --bg-secondary: #334155;
            --bg-tertiary: #475569;

            --text-primary: #e2e8f0;
            --text-secondary: #94a3b8;
            --text-tertiary: #64748b;

            --border-primary: #475569;
            --border-secondary: #64748b;

            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.2);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-dark) 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
            transition: background 0.3s ease;
        }

        body.dark-mode {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
        }

        .auth-container {
            background: var(--bg-primary);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
            overflow: hidden;
            width: 100%;
            max-width: 400px;
            position: relative;
        }

        .auth-header {
            text-align: center;
            padding: 2rem 2rem 1rem;
            background: var(--bg-secondary);
        }

        .auth-logo {
            width: 4rem;
            height: 4rem;
            background: var(--color-primary);
            border-radius: var(--radius-xl);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            color: white;
            font-size: 1.5rem;
        }

        .auth-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .auth-subtitle {
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        .auth-form {
            padding: 2rem;
        }

        .form-tabs {
            display: flex;
            background: var(--bg-tertiary);
            border-radius: var(--radius-lg);
            padding: 0.25rem;
            margin-bottom: 2rem;
        }

        .tab-btn {
            flex: 1;
            padding: 0.75rem 1rem;
            border: none;
            background: transparent;
            border-radius: var(--radius-md);
            font-weight: 500;
            color: var(--text-secondary);
            cursor: pointer;
            transition: all var(--transition-fast);
        }

        .tab-btn.active {
            background: var(--bg-primary);
            color: var(--text-primary);
            box-shadow: var(--shadow-sm);
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            font-size: 0.875rem;
        }

        .form-input {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 1px solid var(--border-primary);
            border-radius: var(--radius-lg);
            font-size: 1rem;
            transition: all var(--transition-fast);
            background: var(--bg-primary);
        }

        .form-input:focus {
            outline: none;
            border-color: var(--color-primary);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        .form-input.error {
            border-color: var(--color-danger);
        }
        .password-input-container {
            position: relative;
        }

        .password-input-container .form-input {
            padding-right: 3rem;
        }

        .password-toggle {
            position: absolute;
            right: 0.75rem;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            padding: 0.25rem;
            border-radius: var(--radius-md);
            transition: all var(--transition-fast);
            display: flex;
            align-items: center;
            justify-content: center;
            width: 2rem;
            height: 2rem;
        }

        .password-toggle:hover {
            color: var(--text-primary);
            background: var(--bg-secondary);
        }

        .password-toggle:focus {
            outline: 2px solid var(--color-primary);
            outline-offset: 2px;
        }

        .btn {
            width: 100%;
            padding: 0.75rem 1rem;
            border: none;
            border-radius: var(--radius-lg);
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all var(--transition-fast);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: var(--color-primary);
            color: white;
        }

        .btn-primary:hover:not(:disabled) {
            background: var(--color-primary-dark);
            transform: translateY(-1px);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none !important;
        }

        .alert {
            padding: 0.75rem 1rem;
            border-radius: var(--radius-lg);
            margin-bottom: 1rem;
            font-size: 0.875rem;
        }

        .alert-success {
            background: rgba(16, 185, 129, 0.1);
            color: var(--color-success);
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .alert-error {
            background: rgba(239, 68, 68, 0.1);
            color: var(--color-danger);
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        .toggle-link {
            text-align: center;
            margin-top: 1rem;
            color: var(--color-primary);
            cursor: pointer;
            font-size: 0.875rem;
            font-weight: 500;
            transition: color var(--transition-fast);
        }

        .toggle-link:hover {
            text-decoration: underline;
        }

        .hidden {
            display: none !important;
        }
        /* Dark Mode Toggle */
        .dark-mode-toggle {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: none;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            width: 2.5rem;
            height: 2.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            color: rgba(255, 255, 255, 0.8);
        }

        .dark-mode-toggle:hover {
            border-color: rgba(255, 255, 255, 0.6);
            color: white;
            transform: rotate(180deg);
        }

        body.dark-mode .dark-mode-toggle {
            border-color: rgba(226, 232, 240, 0.3);
            color: rgba(226, 232, 240, 0.8);
        }

        body.dark-mode .dark-mode-toggle:hover {
            border-color: rgba(226, 232, 240, 0.6);
            color: #e2e8f0;
        }

        @media (max-width: 480px) {
            .auth-container {
                margin: 0;
                border-radius: 0;
                min-height: 100vh;
                display: flex;
                flex-direction: column;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <button class="dark-mode-toggle" onclick="toggleDarkMode()" title="Toggle Dark Mode">
        <i class="fas fa-moon" id="dark-mode-icon"></i>
    </button>

    <div class="auth-container">
        <div class="auth-header">
            <div class="auth-logo">
                <i class="fas fa-brain"></i>
            </div>
            <h1 class="auth-title">AI Text Detector</h1>
            <p class="auth-subtitle">Advanced AI content detection system</p>
        </div>

        <div class="auth-form">
            <div class="form-tabs">
                <button type="button" class="tab-btn active" onclick="switchTab('login')">Login</button>
                <button type="button" class="tab-btn" onclick="switchTab('signup')">Register</button>
            </div>

            <!-- Display Messages -->
            <?php if (isset($error_message) && !empty($error_message)): ?>
                <div class="alert alert-error"><?= htmlspecialchars($error_message) ?></div>
            <?php endif; ?>

            <?php if (isset($signup_error_message) && !empty($signup_error_message)): ?>
                <div class="alert alert-error"><?= htmlspecialchars($signup_error_message) ?></div>
            <?php endif; ?>

            <?php if (isset($signup_success_message) && !empty($signup_success_message)): ?>
                <div class="alert alert-success"><?= htmlspecialchars($signup_success_message) ?></div>
            <?php endif; ?>

            <!-- Login Form -->
            <form id="loginForm" action="index.php" method="post" class="auth-form-content">
                <div class="form-group">
                    <label class="form-label" for="username">Username</label>
                    <input type="text" id="username" name="username" class="form-input" placeholder="Enter your username" required>
                </div>

                <div class="form-group">
                    <label class="form-label" for="password">Password</label>
                    <div class="password-input-container">
                        <input type="password" id="password" name="password" class="form-input" placeholder="Enter your password" required>
                        <button type="button" class="password-toggle" onclick="togglePassword('password')" title="Show/Hide Password">
                            <i class="fas fa-eye" id="password-icon"></i>
                        </button>
                    </div>
                </div>

                <button type="submit" name="login" class="btn btn-primary">
                    <span>Sign In</span>
                </button>
            </form>

            <!-- Signup Form -->
            <form id="signupForm" action="index.php" method="post" class="auth-form-content hidden">
                <div class="form-group">
                    <label class="form-label" for="signup-username">Username</label>
                    <input type="text" id="signup-username" name="username" class="form-input" placeholder="Choose a username" required>
                </div>

                <div class="form-group">
                    <label class="form-label" for="email">Email</label>
                    <input type="email" id="email" name="email" class="form-input" placeholder="Enter your email" required>
                </div>

                <div class="form-group">
                    <label class="form-label" for="signup-password">Password</label>
                    <div class="password-input-container">
                        <input type="password" id="signup-password" name="password" class="form-input" placeholder="Create a password" required>
                        <button type="button" class="password-toggle" onclick="togglePassword('signup-password')" title="Show/Hide Password">
                            <i class="fas fa-eye" id="signup-password-icon"></i>
                        </button>
                    </div>
                </div>

                <button type="submit" name="signup" class="btn btn-primary">
                    <span>Create Account</span>
                </button>
            </form>
        </div>
    </div>

    <script>
        // Dark Mode Functionality
        function toggleDarkMode() {
            const body = document.body;
            const icon = document.getElementById('dark-mode-icon');

            body.classList.toggle('dark-mode');

            // Update icon
            if (body.classList.contains('dark-mode')) {
                icon.classList.remove('fa-moon');
                icon.classList.add('fa-sun');
            } else {
                icon.classList.remove('fa-sun');
                icon.classList.add('fa-moon');
            }

            // Save preference
            localStorage.setItem('darkMode', body.classList.contains('dark-mode'));
        }

        // Load dark mode preference
        window.addEventListener('load', function() {
            const darkMode = localStorage.getItem('darkMode') === 'true';
            const body = document.body;
            const icon = document.getElementById('dark-mode-icon');

            if (darkMode) {
                body.classList.add('dark-mode');
                icon.classList.remove('fa-moon');
                icon.classList.add('fa-sun');
            }
        });

        // Tab switching functionality
        function switchTab(tab) {
            const loginForm = document.getElementById('loginForm');
            const signupForm = document.getElementById('signupForm');
            const tabBtns = document.querySelectorAll('.tab-btn');

            // Update tab buttons
            tabBtns.forEach(btn => btn.classList.remove('active'));

            if (tab === 'login') {
                loginForm.classList.remove('hidden');
                signupForm.classList.add('hidden');
                tabBtns[0].classList.add('active');
            } else {
                loginForm.classList.add('hidden');
                signupForm.classList.remove('hidden');
                tabBtns[1].classList.add('active');
            }
        }

        // Password toggle functionality
        function togglePassword(inputId) {
            const passwordInput = document.getElementById(inputId);
            const toggleIcon = document.getElementById(inputId + '-icon');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        }
    </script>
</body>
</html>
